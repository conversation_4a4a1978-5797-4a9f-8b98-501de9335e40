#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - 图形用户界面
功能：为main_controller.py提供可视化操作界面

核心特性：
1. 实时监控执行状态和进度
2. 可视化配置管理
3. 日志实时显示
4. 统计信息展示
5. 一键启动/停止控制
6. 多窗口状态监控

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import queue
import json
import time
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Optional

# 导入主控制器
from main_controller import WeChatMainController, ExecutionStep, WindowStatus


class WeChatAutomationGUI:
    """微信自动化添加好友图形用户界面"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("微信自动化添加好友控制台 v1.0.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 控制器和状态
        self.controller: Optional[WeChatMainController] = None
        self.is_running = False
        self.automation_thread: Optional[threading.Thread] = None
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()
        
        # 状态数据
        self.execution_stats = {
            "total_contacts": 0,
            "processed_contacts": 0,
            "successful_adds": 0,
            "failed_adds": 0,
            "skipped_contacts": 0,
            "total_windows": 0,
            "completed_windows": 0,
            "start_time": None,
            "end_time": None
        }
        
        # 创建界面
        self.create_widgets()
        self.setup_logging()
        
        # 启动消息处理
        self.process_messages()
        
        # 加载配置
        self.load_configuration()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Success.TLabel', foreground='green', font=('Arial', 10, 'bold'))
        style.configure('Error.TLabel', foreground='red', font=('Arial', 10, 'bold'))
        style.configure('Warning.TLabel', foreground='orange', font=('Arial', 10, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.create_main_frame()
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_content_area()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建主框架"""
        # 主容器
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开配置文件", command=self.open_config_file)
        file_menu.add_command(label="打开Excel文件", command=self.open_excel_file)
        file_menu.add_separator()
        file_menu.add_command(label="导出日志", command=self.export_logs)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="配置验证", command=self.validate_configuration)
        tools_menu.add_command(label="清理日志", command=self.clean_logs)
        tools_menu.add_command(label="重置统计", command=self.reset_statistics)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 主要控制按钮
        self.start_button = ttk.Button(
            toolbar, text="▶ 开始自动化", 
            command=self.start_automation,
            style='Success.TButton'
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(
            toolbar, text="⏹ 停止", 
            command=self.stop_automation,
            state=tk.DISABLED,
            style='Error.TButton'
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.pause_button = ttk.Button(
            toolbar, text="⏸ 暂停", 
            command=self.pause_automation,
            state=tk.DISABLED
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 分隔线
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 配置按钮
        ttk.Button(
            toolbar, text="⚙ 配置", 
            command=self.open_config_dialog
        ).pack(side=tk.LEFT, padx=(5, 5))
        
        ttk.Button(
            toolbar, text="📊 统计", 
            command=self.show_statistics_dialog
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧状态指示器
        self.status_indicator = ttk.Label(
            toolbar, text="● 就绪", 
            style='Success.TLabel'
        )
        self.status_indicator.pack(side=tk.RIGHT)
        
    def create_content_area(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_control_tab()
        self.create_progress_tab()
        self.create_windows_tab()
        self.create_logs_tab()
        self.create_config_tab()
        
    def create_control_tab(self):
        """创建控制标签页"""
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="🎮 控制面板")
        
        # 左侧控制区域
        left_frame = ttk.LabelFrame(control_frame, text="执行控制", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 文件选择
        file_frame = ttk.Frame(left_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(file_frame, text="Excel文件:").pack(anchor=tk.W)
        excel_frame = ttk.Frame(file_frame)
        excel_frame.pack(fill=tk.X, pady=(2, 0))
        
        self.excel_path_var = tk.StringVar(value="添加好友名单.xlsx")
        self.excel_entry = ttk.Entry(excel_frame, textvariable=self.excel_path_var)
        self.excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            excel_frame, text="浏览", 
            command=self.browse_excel_file
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 执行参数
        params_frame = ttk.LabelFrame(left_frame, text="执行参数", padding=5)
        params_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 批次大小
        batch_frame = ttk.Frame(params_frame)
        batch_frame.pack(fill=tk.X, pady=2)
        ttk.Label(batch_frame, text="批次大小:").pack(side=tk.LEFT)
        self.batch_size_var = tk.StringVar(value="10")
        ttk.Entry(batch_frame, textvariable=self.batch_size_var, width=10).pack(side=tk.RIGHT)
        
        # 延迟设置
        delay_frame = ttk.Frame(params_frame)
        delay_frame.pack(fill=tk.X, pady=2)
        ttk.Label(delay_frame, text="操作延迟(秒):").pack(side=tk.LEFT)
        self.delay_var = tk.StringVar(value="2.0")
        ttk.Entry(delay_frame, textvariable=self.delay_var, width=10).pack(side=tk.RIGHT)
        
        # 右侧状态区域
        right_frame = ttk.LabelFrame(control_frame, text="实时状态", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 当前状态显示
        self.current_status_label = ttk.Label(
            right_frame, text="当前状态: 就绪", 
            style='Title.TLabel'
        )
        self.current_status_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.current_step_label = ttk.Label(
            right_frame, text="执行步骤: 无", 
            style='Status.TLabel'
        )
        self.current_step_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.current_window_label = ttk.Label(
            right_frame, text="当前窗口: 无", 
            style='Status.TLabel'
        )
        self.current_window_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 快速统计
        stats_frame = ttk.LabelFrame(right_frame, text="快速统计", padding=5)
        stats_frame.pack(fill=tk.X)
        
        self.stats_labels = {}
        stats_items = [
            ("总联系人", "total_contacts"),
            ("已处理", "processed_contacts"),
            ("成功添加", "successful_adds"),
            ("失败", "failed_adds")
        ]
        
        for i, (label, key) in enumerate(stats_items):
            frame = ttk.Frame(stats_frame)
            frame.pack(fill=tk.X, pady=1)
            ttk.Label(frame, text=f"{label}:").pack(side=tk.LEFT)
            self.stats_labels[key] = ttk.Label(frame, text="0", style='Status.TLabel')
            self.stats_labels[key].pack(side=tk.RIGHT)

    def create_progress_tab(self):
        """创建进度监控标签页"""
        progress_frame = ttk.Frame(self.notebook)
        self.notebook.add(progress_frame, text="📈 进度监控")

        # 总体进度
        overall_frame = ttk.LabelFrame(progress_frame, text="总体进度", padding=10)
        overall_frame.pack(fill=tk.X, padx=5, pady=5)

        # 联系人处理进度
        ttk.Label(overall_frame, text="联系人处理进度:", style='Title.TLabel').pack(anchor=tk.W)
        self.contact_progress = ttk.Progressbar(
            overall_frame, mode='determinate', length=400
        )
        self.contact_progress.pack(fill=tk.X, pady=(5, 0))

        self.contact_progress_label = ttk.Label(
            overall_frame, text="0 / 0 (0%)", style='Status.TLabel'
        )
        self.contact_progress_label.pack(anchor=tk.W, pady=(2, 10))

        # 窗口处理进度
        ttk.Label(overall_frame, text="窗口处理进度:", style='Title.TLabel').pack(anchor=tk.W)
        self.window_progress = ttk.Progressbar(
            overall_frame, mode='determinate', length=400
        )
        self.window_progress.pack(fill=tk.X, pady=(5, 0))

        self.window_progress_label = ttk.Label(
            overall_frame, text="0 / 0 (0%)", style='Status.TLabel'
        )
        self.window_progress_label.pack(anchor=tk.W, pady=(2, 0))

        # 详细统计
        details_frame = ttk.LabelFrame(progress_frame, text="详细统计", padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建统计表格
        columns = ("项目", "数量", "百分比", "状态")
        self.stats_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.stats_tree.heading(col, text=col)
            self.stats_tree.column(col, width=100)

        # 滚动条
        stats_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.stats_tree.yview)
        self.stats_tree.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化统计数据
        self.update_statistics_display()

    def create_windows_tab(self):
        """创建窗口监控标签页"""
        windows_frame = ttk.Frame(self.notebook)
        self.notebook.add(windows_frame, text="🪟 窗口监控")

        # 窗口列表
        list_frame = ttk.LabelFrame(windows_frame, text="微信窗口列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建窗口表格
        window_columns = ("序号", "窗口标题", "句柄", "状态", "当前步骤", "处理联系人数")
        self.windows_tree = ttk.Treeview(list_frame, columns=window_columns, show='headings', height=10)

        for col in window_columns:
            self.windows_tree.heading(col, text=col)
            if col == "窗口标题":
                self.windows_tree.column(col, width=200)
            elif col == "句柄":
                self.windows_tree.column(col, width=80)
            else:
                self.windows_tree.column(col, width=100)

        # 滚动条
        windows_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.windows_tree.yview)
        self.windows_tree.configure(yscrollcommand=windows_scrollbar.set)

        self.windows_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        windows_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 窗口操作按钮
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(
            button_frame, text="刷新窗口列表",
            command=self.refresh_windows_list
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            button_frame, text="激活选中窗口",
            command=self.activate_selected_window
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            button_frame, text="关闭选中窗口",
            command=self.close_selected_window
        ).pack(side=tk.LEFT)

    def create_logs_tab(self):
        """创建日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 日志")

        # 日志控制
        log_control_frame = ttk.Frame(logs_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(log_control_frame, text="日志级别:").pack(side=tk.LEFT)
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(
            log_control_frame, textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly", width=10
        )
        log_level_combo.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Button(
            log_control_frame, text="清空日志",
            command=self.clear_logs
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            log_control_frame, text="保存日志",
            command=self.save_logs
        ).pack(side=tk.LEFT)

        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            log_control_frame, text="自动滚动",
            variable=self.auto_scroll_var
        ).pack(side=tk.RIGHT)

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(
            logs_frame, wrap=tk.WORD, height=20,
            font=('Consolas', 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # 配置日志颜色
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("DEBUG", foreground="gray")

    def create_config_tab(self):
        """创建配置标签页"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙ 配置")

        # 配置编辑器
        config_control_frame = ttk.Frame(config_frame)
        config_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            config_control_frame, text="加载配置",
            command=self.load_configuration
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            config_control_frame, text="保存配置",
            command=self.save_configuration
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            config_control_frame, text="重置为默认",
            command=self.reset_configuration
        ).pack(side=tk.LEFT)

        # 配置文本编辑器
        self.config_text = scrolledtext.ScrolledText(
            config_frame, wrap=tk.WORD, height=25,
            font=('Consolas', 9)
        )
        self.config_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态信息
        self.status_text = ttk.Label(
            self.status_bar, text="就绪",
            relief=tk.SUNKEN, anchor=tk.W
        )
        self.status_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 时间显示
        self.time_label = ttk.Label(
            self.status_bar, text="",
            relief=tk.SUNKEN, anchor=tk.E, width=20
        )
        self.time_label.pack(side=tk.RIGHT, padx=(0, 5))

        # 更新时间
        self.update_time()

    def setup_logging(self):
        """设置日志处理"""
        # 创建自定义日志处理器
        self.log_handler = GUILogHandler(self.message_queue)
        self.log_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.log_handler.setFormatter(formatter)

        # 添加到根日志记录器
        logging.getLogger().addHandler(self.log_handler)

    # ==================== 核心功能方法 ====================

    def start_automation(self):
        """开始自动化流程"""
        if self.is_running:
            messagebox.showwarning("警告", "自动化流程已在运行中")
            return

        try:
            # 验证配置
            if not self.validate_before_start():
                return

            # 更新界面状态
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
            self.status_indicator.config(text="● 运行中", style='Warning.TLabel')
            self.update_status("正在启动自动化流程...")

            # 启动自动化线程
            self.automation_thread = threading.Thread(
                target=self.run_automation_thread,
                daemon=True
            )
            self.automation_thread.start()

            self.log_message("INFO", "自动化流程已启动")

        except Exception as e:
            self.log_message("ERROR", f"启动自动化流程失败: {e}")
            self.reset_ui_state()

    def stop_automation(self):
        """停止自动化流程"""
        if not self.is_running:
            return

        try:
            self.is_running = False
            self.update_status("正在停止自动化流程...")

            # 停止控制器
            if self.controller:
                # 这里可以添加控制器的停止方法
                pass

            self.reset_ui_state()
            self.log_message("INFO", "自动化流程已停止")

        except Exception as e:
            self.log_message("ERROR", f"停止自动化流程失败: {e}")

    def pause_automation(self):
        """暂停/恢复自动化流程"""
        # 这里可以实现暂停逻辑
        pass

    def run_automation_thread(self):
        """在后台线程中运行自动化流程"""
        try:
            # 初始化控制器
            excel_file = self.excel_path_var.get()
            config_file = "config.json"

            self.controller = WeChatMainController(excel_file, config_file)

            # 获取微信窗口
            self.update_status("正在扫描微信窗口...")
            windows = self.controller.get_wechat_windows()

            if not windows:
                self.log_message("ERROR", "未找到微信窗口")
                self.reset_ui_state()
                return

            # 加载联系人数据
            self.update_status("正在加载联系人数据...")
            contacts = self.controller.load_contacts_data()

            if not contacts:
                self.log_message("ERROR", "未找到待处理的联系人")
                self.reset_ui_state()
                return

            # 更新统计信息
            self.execution_stats.update(self.controller.execution_stats)
            self.update_statistics_display()
            self.update_windows_display(windows)

            # 移动所有窗口到指定位置
            self.update_status("正在移动微信窗口...")
            if not self.controller.move_all_windows_to_target_position(windows):
                self.log_message("WARNING", "窗口移动失败，但继续执行")

            # 执行多窗口流程
            self.update_status("正在执行自动化流程...")
            result = self.controller.execute_multi_window_flow(windows, contacts)

            if result:
                self.log_message("INFO", "自动化流程执行完成")
                self.update_status("自动化流程执行完成")
            else:
                self.log_message("ERROR", "自动化流程执行失败")
                self.update_status("自动化流程执行失败")

        except Exception as e:
            self.log_message("ERROR", f"自动化流程异常: {e}")
            self.update_status(f"自动化流程异常: {e}")
        finally:
            self.reset_ui_state()

    def validate_before_start(self) -> bool:
        """启动前验证"""
        # 检查Excel文件
        excel_file = self.excel_path_var.get()
        if not excel_file or not Path(excel_file).exists():
            messagebox.showerror("错误", "请选择有效的Excel文件")
            return False

        # 检查配置文件
        if not Path("config.json").exists():
            messagebox.showerror("错误", "配置文件config.json不存在")
            return False

        return True

    def reset_ui_state(self):
        """重置界面状态"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.DISABLED)
        self.status_indicator.config(text="● 就绪", style='Success.TLabel')

    # ==================== 界面更新方法 ====================

    def update_status(self, message: str):
        """更新状态信息"""
        self.message_queue.put(('status', message))

    def log_message(self, level: str, message: str):
        """添加日志消息"""
        self.message_queue.put(('log', level, message))

    def update_statistics_display(self):
        """更新统计显示"""
        # 更新快速统计标签
        for key, label in self.stats_labels.items():
            value = self.execution_stats.get(key, 0)
            label.config(text=str(value))

        # 更新进度条
        total_contacts = self.execution_stats.get("total_contacts", 0)
        processed_contacts = self.execution_stats.get("processed_contacts", 0)

        if total_contacts > 0:
            progress_percent = (processed_contacts / total_contacts) * 100
            self.contact_progress.config(value=progress_percent)
            self.contact_progress_label.config(
                text=f"{processed_contacts} / {total_contacts} ({progress_percent:.1f}%)"
            )

        total_windows = self.execution_stats.get("total_windows", 0)
        completed_windows = self.execution_stats.get("completed_windows", 0)

        if total_windows > 0:
            window_progress_percent = (completed_windows / total_windows) * 100
            self.window_progress.config(value=window_progress_percent)
            self.window_progress_label.config(
                text=f"{completed_windows} / {total_windows} ({window_progress_percent:.1f}%)"
            )

        # 更新详细统计表格
        self.stats_tree.delete(*self.stats_tree.get_children())

        stats_data = [
            ("总联系人", self.execution_stats.get("total_contacts", 0)),
            ("已处理", self.execution_stats.get("processed_contacts", 0)),
            ("成功添加", self.execution_stats.get("successful_adds", 0)),
            ("失败", self.execution_stats.get("failed_adds", 0)),
            ("跳过", self.execution_stats.get("skipped_contacts", 0)),
            ("总窗口", self.execution_stats.get("total_windows", 0)),
            ("完成窗口", self.execution_stats.get("completed_windows", 0))
        ]

        for name, count in stats_data:
            total = self.execution_stats.get("total_contacts", 1)
            if name in ["总窗口", "完成窗口"]:
                total = self.execution_stats.get("total_windows", 1)

            percentage = (count / total * 100) if total > 0 else 0
            status = "完成" if percentage == 100 else "进行中" if percentage > 0 else "待开始"

            self.stats_tree.insert("", tk.END, values=(
                name, count, f"{percentage:.1f}%", status
            ))

    def update_windows_display(self, windows: List[Dict]):
        """更新窗口显示"""
        self.windows_tree.delete(*self.windows_tree.get_children())

        for i, window in enumerate(windows):
            hwnd = window.get('hwnd', 'Unknown')
            title = window.get('title', 'Unknown')
            status = "待处理"  # 可以从控制器获取实际状态
            step = "无"
            contacts = 0

            self.windows_tree.insert("", tk.END, values=(
                i + 1, title, hwnd, status, step, contacts
            ))

    def process_messages(self):
        """处理消息队列"""
        try:
            while True:
                message = self.message_queue.get_nowait()

                if message[0] == 'status':
                    self.status_text.config(text=message[1])
                    self.current_status_label.config(text=f"当前状态: {message[1]}")

                elif message[0] == 'log':
                    level, text = message[1], message[2]
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_line = f"[{timestamp}] {level}: {text}\n"

                    self.log_text.insert(tk.END, log_line, level)

                    if self.auto_scroll_var.get():
                        self.log_text.see(tk.END)

        except queue.Empty:
            pass
        finally:
            # 每100ms检查一次消息队列
            self.root.after(100, self.process_messages)

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # ==================== 文件操作方法 ====================

    def browse_excel_file(self):
        """浏览Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_path_var.set(filename)

    def open_config_file(self):
        """打开配置文件"""
        try:
            import os
            os.startfile("config.json")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开配置文件: {e}")

    def open_excel_file(self):
        """打开Excel文件"""
        try:
            import os
            excel_file = self.excel_path_var.get()
            if excel_file and Path(excel_file).exists():
                os.startfile(excel_file)
            else:
                messagebox.showerror("错误", "Excel文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开Excel文件: {e}")

    def load_configuration(self):
        """加载配置"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config_data = f.read()

            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, config_data)

            # 解析配置并更新界面
            config = json.loads(config_data)
            self.batch_size_var.set(str(config.get("batch_size", 10)))
            delay_range = config.get("delay_range", [1.5, 3.0])
            self.delay_var.set(str(delay_range[0]))

            self.log_message("INFO", "配置文件加载成功")

        except Exception as e:
            self.log_message("ERROR", f"加载配置文件失败: {e}")

    def save_configuration(self):
        """保存配置"""
        try:
            config_data = self.config_text.get(1.0, tk.END).strip()

            # 验证JSON格式
            json.loads(config_data)

            with open("config.json", "w", encoding="utf-8") as f:
                f.write(config_data)

            self.log_message("INFO", "配置文件保存成功")
            messagebox.showinfo("成功", "配置文件保存成功")

        except json.JSONDecodeError as e:
            messagebox.showerror("错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置文件失败: {e}")

    def reset_configuration(self):
        """重置配置为默认值"""
        if messagebox.askyesno("确认", "确定要重置配置为默认值吗？"):
            try:
                # 这里可以加载默认配置
                self.load_configuration()
                self.log_message("INFO", "配置已重置为默认值")
            except Exception as e:
                self.log_message("ERROR", f"重置配置失败: {e}")

    # ==================== 工具方法 ====================

    def validate_configuration(self):
        """验证配置"""
        try:
            # 检查配置文件
            if not Path("config.json").exists():
                messagebox.showerror("错误", "配置文件不存在")
                return

            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 检查Excel文件
            excel_file = self.excel_path_var.get()
            if not excel_file or not Path(excel_file).exists():
                messagebox.showerror("错误", "Excel文件不存在")
                return

            # 检查必要的模块
            required_modules = ["modules", "modules.window_manager", "modules.data_manager"]
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError as e:
                    messagebox.showerror("错误", f"缺少必要模块: {module}")
                    return

            messagebox.showinfo("成功", "配置验证通过")
            self.log_message("INFO", "配置验证通过")

        except Exception as e:
            messagebox.showerror("错误", f"配置验证失败: {e}")

    def refresh_windows_list(self):
        """刷新窗口列表"""
        try:
            if self.controller:
                windows = self.controller.get_wechat_windows()
                self.update_windows_display(windows)
                self.log_message("INFO", f"刷新窗口列表完成，发现 {len(windows)} 个窗口")
            else:
                # 临时创建控制器来获取窗口列表
                temp_controller = WeChatMainController()
                windows = temp_controller.get_wechat_windows()
                self.update_windows_display(windows)
                self.log_message("INFO", f"刷新窗口列表完成，发现 {len(windows)} 个窗口")
        except Exception as e:
            self.log_message("ERROR", f"刷新窗口列表失败: {e}")

    def activate_selected_window(self):
        """激活选中的窗口"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return

        try:
            item = self.windows_tree.item(selection[0])
            hwnd = item['values'][2]  # 句柄在第3列

            if self.controller and self.controller.window_manager:
                self.controller.window_manager.activate_window(int(hwnd))
                self.log_message("INFO", f"已激活窗口: {hwnd}")
            else:
                messagebox.showwarning("警告", "控制器未初始化")
        except Exception as e:
            self.log_message("ERROR", f"激活窗口失败: {e}")

    def close_selected_window(self):
        """关闭选中的窗口"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return

        if not messagebox.askyesno("确认", "确定要关闭选中的窗口吗？"):
            return

        try:
            item = self.windows_tree.item(selection[0])
            hwnd = item['values'][2]  # 句柄在第3列

            if self.controller and self.controller.window_manager:
                self.controller.window_manager.close_window(int(hwnd))
                self.refresh_windows_list()
                self.log_message("INFO", f"已关闭窗口: {hwnd}")
            else:
                messagebox.showwarning("警告", "控制器未初始化")
        except Exception as e:
            self.log_message("ERROR", f"关闭窗口失败: {e}")

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("INFO", "日志已清空")

    def save_logs(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)

                messagebox.showinfo("成功", f"日志已保存到: {filename}")
                self.log_message("INFO", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def export_logs(self):
        """导出日志"""
        self.save_logs()

    def clean_logs(self):
        """清理日志文件"""
        try:
            logs_dir = Path("logs")
            if logs_dir.exists():
                # 这里可以实现日志清理逻辑
                messagebox.showinfo("信息", "日志清理功能待实现")
            else:
                messagebox.showinfo("信息", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"清理日志失败: {e}")

    def reset_statistics(self):
        """重置统计信息"""
        if messagebox.askyesno("确认", "确定要重置统计信息吗？"):
            self.execution_stats = {
                "total_contacts": 0,
                "processed_contacts": 0,
                "successful_adds": 0,
                "failed_adds": 0,
                "skipped_contacts": 0,
                "total_windows": 0,
                "completed_windows": 0,
                "start_time": None,
                "end_time": None
            }
            self.update_statistics_display()
            self.log_message("INFO", "统计信息已重置")

    def open_config_dialog(self):
        """打开配置对话框"""
        # 切换到配置标签页
        self.notebook.select(4)  # 配置标签页的索引

    def show_statistics_dialog(self):
        """显示统计对话框"""
        # 切换到进度监控标签页
        self.notebook.select(1)  # 进度监控标签页的索引

    def show_help(self):
        """显示帮助信息"""
        help_text = """
微信自动化添加好友控制台 v1.0.0

使用说明：
1. 在"控制面板"中选择Excel文件
2. 配置执行参数
3. 点击"开始自动化"按钮
4. 在"进度监控"中查看执行进度
5. 在"窗口监控"中管理微信窗口
6. 在"日志"中查看详细执行信息

注意事项：
- 确保微信已登录并可见
- Excel文件格式必须正确
- 建议在执行前备份数据
- 遇到问题请查看日志信息

技术支持：
- 查看README.md获取详细说明
- 检查config.json配置文件
- 查看logs目录下的日志文件
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x400")
        help_window.resizable(False, False)

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """
微信自动化添加好友控制台
版本：v1.0.0
创建时间：2025-01-28

功能特性：
• 6步骤严格执行流程
• 多窗口循环处理
• 智能错误处理
• 实时进度监控
• 可视化操作界面

技术架构：
• Python + tkinter
• 模块化设计
• 多线程处理
• 配置驱动

开发者：AI助手
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            if messagebox.askyesno("确认", "自动化流程正在运行，确定要退出吗？"):
                self.stop_automation()
                self.root.after(1000, self.root.destroy)
        else:
            self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


class GUILogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI"""

    def __init__(self, message_queue):
        super().__init__()
        self.message_queue = message_queue

    def emit(self, record):
        try:
            msg = self.format(record)
            self.message_queue.put(('log', record.levelname, msg))
        except Exception:
            self.handleError(record)


def main():
    """主程序入口"""
    try:
        # 创建并运行GUI
        app = WeChatAutomationGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
